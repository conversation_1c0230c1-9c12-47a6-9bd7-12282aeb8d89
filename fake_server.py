#!/usr/bin/env python3
"""
Simple fake server for DDoS testing
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import time
from datetime import datetime
import threading

class RequestTracker:
    def __init__(self):
        self.request_count = 0
        self.lock = threading.Lock()
        self.start_time = time.time()
        
    def add_request(self):
        with self.lock:
            self.request_count += 1
    
    def get_stats(self):
        with self.lock:
            uptime = time.time() - self.start_time
            rps = self.request_count / uptime if uptime > 0 else 0
            return {
                'total_requests': self.request_count,
                'uptime_seconds': uptime,
                'rps': rps
            }

class ServerHandler(BaseHTTPRequestHandler):
    tracker = RequestTracker()
    
    def do_GET(self):
        self.tracker.add_request()
        
        # Log request
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {self.client_address[0]} -> {self.path}")
        
        if self.path == '/':
            self.send_home_page()
        elif self.path == '/stats':
            self.send_stats()
        else:
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'<h1>Fake Server Response</h1>')
    
    def send_home_page(self):
        stats = self.tracker.get_stats()
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Fake Server</title>
            <meta http-equiv="refresh" content="2">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .stats {{ background: #f0f0f0; padding: 20px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <h1>🎯 Fake Server for DDoS Testing</h1>
            <div class="stats">
                <h3>📊 Server Statistics</h3>
                <p>Total Requests: <strong>{stats['total_requests']}</strong></p>
                <p>Uptime: <strong>{stats['uptime_seconds']:.1f}s</strong></p>
                <p>Requests per Second: <strong>{stats['rps']:.1f}</strong></p>
            </div>
            <p>Auto-refreshes every 2 seconds</p>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_stats(self):
        stats = self.tracker.get_stats()
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(stats, indent=2).encode())
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass

def run_server(port=8080):
    server_address = ('', port)
    httpd = HTTPServer(server_address, ServerHandler)
    
    print("=" * 50)
    print("🚀 Fake Server Starting")
    print("=" * 50)
    print(f"Server: http://localhost:{port}")
    print(f"Stats: http://localhost:{port}/stats")
    print("=" * 50)
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == '__main__':
    import sys
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8080
    run_server(port)
