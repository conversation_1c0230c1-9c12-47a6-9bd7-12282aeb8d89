#!/usr/bin/env python3
"""
Simple test to verify the DDoS simulator and server work together
"""

import requests
import time
import threading
from datetime import datetime

def test_single_request():
    """Test a single request to the server"""
    print("🔍 Testing single request...")
    try:
        response = requests.get("http://localhost:8080", timeout=5)
        print(f"✅ Single request successful! Status: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Single request failed: {e}")
        return False

def test_multiple_requests(count=10):
    """Test multiple sequential requests"""
    print(f"\n🔍 Testing {count} sequential requests...")
    successful = 0
    failed = 0
    
    for i in range(count):
        try:
            response = requests.get("http://localhost:8080", timeout=2)
            successful += 1
            print(f"Request {i+1}: ✅ Status {response.status_code}")
        except Exception as e:
            failed += 1
            print(f"Request {i+1}: ❌ Failed - {e}")
        
        time.sleep(0.1)  # Small delay
    
    print(f"\n📊 Results: {successful} successful, {failed} failed")
    return successful > 0

def test_concurrent_requests(threads=5, requests_per_thread=3):
    """Test concurrent requests (mini DDoS)"""
    print(f"\n🔍 Testing {threads} concurrent threads, {requests_per_thread} requests each...")
    
    successful = 0
    failed = 0
    lock = threading.Lock()
    
    def worker():
        nonlocal successful, failed
        for i in range(requests_per_thread):
            try:
                response = requests.get("http://localhost:8080", timeout=2)
                with lock:
                    successful += 1
                print(f"Thread {threading.current_thread().name}: ✅ Request {i+1} - Status {response.status_code}")
            except Exception as e:
                with lock:
                    failed += 1
                print(f"Thread {threading.current_thread().name}: ❌ Request {i+1} - Failed: {e}")
            
            time.sleep(0.05)  # Small delay
    
    # Start threads
    threads_list = []
    for i in range(threads):
        t = threading.Thread(target=worker, name=f"T{i+1}")
        threads_list.append(t)
        t.start()
    
    # Wait for all threads
    for t in threads_list:
        t.join()
    
    total_requests = threads * requests_per_thread
    print(f"\n📊 Concurrent test results:")
    print(f"   Total requests: {total_requests}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {failed}")
    print(f"   Success rate: {(successful/total_requests)*100:.1f}%")
    
    return successful > 0

def main():
    print("=" * 60)
    print("🧪 DDoS Connection Test")
    print("=" * 60)
    print("This will test if the server and DDoS simulator can communicate")
    print("Make sure the test server is running: python test_server.py")
    print("=" * 60)
    
    # Test 1: Single request
    if not test_single_request():
        print("\n❌ Basic connection failed. Make sure server is running!")
        print("Run: python test_server.py")
        return
    
    # Test 2: Multiple sequential requests
    if not test_multiple_requests(5):
        print("\n❌ Sequential requests failed!")
        return
    
    # Test 3: Concurrent requests (mini DDoS)
    if not test_concurrent_requests(3, 2):
        print("\n❌ Concurrent requests failed!")
        return
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED!")
    print("=" * 60)
    print("✅ Server is responding correctly")
    print("✅ Sequential requests work")
    print("✅ Concurrent requests work")
    print("\nYou can now run the full DDoS simulator:")
    print("   python ddos.py")
    print("   python ddos.py --interactive")
    print("=" * 60)

if __name__ == "__main__":
    main()
