#!/usr/bin/env python3
"""
Intensive DDoS attack against your real server
"""

from ddos import DDoSSimulator

def main():
    print("🔥 INTENSIVE DDoS ATTACK ON YOUR REAL SERVER")
    print("=" * 60)
    
    simulator = DDoSSimulator()
    target = "http://localhost:4200/home"
    
    # Test different attack intensities
    attacks = [
        {"name": "Light Attack", "threads": 10, "requests": 10, "delay": 0.1},
        {"name": "Medium Attack", "threads": 20, "requests": 15, "delay": 0.05},
        {"name": "Heavy Attack", "threads": 50, "requests": 10, "delay": 0},
    ]
    
    for i, attack in enumerate(attacks, 1):
        print(f"\n{'='*60}")
        print(f"🚀 ATTACK {i}/3: {attack['name']}")
        print(f"{'='*60}")
        
        simulator_instance = DDoSSimulator()  # Fresh instance for each attack
        simulator_instance.ddos_attack(
            target_url=target,
            threads=attack['threads'],
            requests_per_thread=attack['requests'],
            delay=attack['delay']
        )
        
        print(f"\n✅ {attack['name']} completed!")
        
        if i < len(attacks):
            print("⏳ Waiting 3 seconds before next attack...")
            import time
            time.sleep(3)
    
    print(f"\n{'='*60}")
    print("🎉 ALL ATTACKS COMPLETED!")
    print("Check your server logs and performance!")
    print("=" * 60)

if __name__ == "__main__":
    main()
