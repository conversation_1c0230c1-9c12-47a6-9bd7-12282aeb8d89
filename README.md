# DDoS Attack Simulator - Educational Tool

⚠️ **EDUCATIONAL PURPOSE ONLY** ⚠️

This project is designed for learning about DDoS attacks and server security. **Never use this against servers you don't own!**

## What's Included

1. **`ddos.py`** - Advanced DDoS simulation script
2. **`test_server.py`** - Target server for testing
3. **`requirements.txt`** - Python dependencies

## Features

### DDoS Simulator (`ddos.py`)
- Multi-threaded HTTP request flooding
- Real-time statistics and monitoring
- Random User-Agent rotation
- Interactive and automated modes
- Configurable attack parameters
- Success/failure rate tracking

### Test Server (`test_server.py`)
- Real-time request monitoring
- Load detection and statistics
- Web dashboard with auto-refresh
- JSON API endpoints
- Request rate tracking
- Client IP monitoring

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the Test Server
```bash
python test_server.py
```
The server will start on `http://localhost:8080`

### 3. Run DDoS Simulation

**Interactive Mode:**
```bash
python ddos.py --interactive
```

**Quick Test:**
```bash
python ddos.py
```

## Usage Examples

### Basic Attack
```python
from ddos import DDoSSimulator

simulator = DDoSSimulator()
simulator.ddos_attack(
    target_url="http://localhost:8080",
    threads=10,
    requests_per_thread=20,
    delay=0.1
)
```

### Monitor Server Response
1. Open `http://localhost:8080` in your browser
2. Run the DDoS simulation
3. Watch real-time statistics and server load

## Server Endpoints

- **`/`** - Main dashboard with real-time stats
- **`/stats`** - JSON statistics API
- **`/health`** - Health check endpoint

## Attack Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `threads` | Number of concurrent threads | 10 |
| `requests_per_thread` | Requests each thread makes | 10 |
| `delay` | Delay between requests (seconds) | 0.1 |
| `target_url` | URL to attack | localhost:8080 |

## Understanding DDoS Attacks

### How It Works
1. **Target Selection**: Attacker chooses a website/server
2. **Request Flooding**: Multiple sources send massive requests
3. **Resource Exhaustion**: Server runs out of CPU/memory/bandwidth
4. **Service Denial**: Legitimate users can't access the service

### Types of DDoS
- **Volume-based**: Overwhelm bandwidth
- **Protocol-based**: Exploit protocol weaknesses
- **Application-based**: Target specific application features

### Defense Strategies
- Rate limiting
- Load balancing
- CDN services
- DDoS protection services
- Traffic filtering

## Educational Objectives

This tool helps you understand:
- How DDoS attacks work technically
- Server resource consumption under load
- The importance of rate limiting
- How to monitor server performance
- Basic DDoS mitigation concepts

## Legal and Ethical Notice

🚨 **IMPORTANT WARNINGS** 🚨

- **Only test against your own servers**
- **DDoS attacks against others are illegal**
- **This is for educational purposes only**
- **Respect others' infrastructure**
- **Use responsibly and ethically**

## Troubleshooting

### Common Issues

**Connection Refused:**
- Make sure the test server is running
- Check if the port is available
- Verify the target URL

**High Failure Rate:**
- Reduce number of threads
- Increase delay between requests
- Check server capacity

**Server Crashes:**
- Lower attack intensity
- Monitor system resources
- Restart the test server

## Advanced Usage

### Custom Attack Patterns
```python
# Slow and steady attack
simulator.ddos_attack(target, threads=5, requests_per_thread=100, delay=1)

# Burst attack
simulator.ddos_attack(target, threads=100, requests_per_thread=5, delay=0)
```

### Monitoring Server Health
```bash
curl http://localhost:8080/health
curl http://localhost:8080/stats
```

## Contributing

This is an educational project. Feel free to:
- Add new attack patterns
- Improve monitoring features
- Enhance the test server
- Add documentation

## License

This project is for educational use only. Use responsibly and ethically.

---

**Remember: With great power comes great responsibility. Use this knowledge to build better, more secure systems!**
