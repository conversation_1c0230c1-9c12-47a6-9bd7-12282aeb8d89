#!/usr/bin/env python3
"""
Simple HTTP server for testing DDoS simulation
This server includes request logging and basic rate limiting detection
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import time
from datetime import datetime
from collections import defaultdict, deque
import threading

class RequestTracker:
    def __init__(self):
        self.requests = deque()
        self.request_count = 0
        self.lock = threading.Lock()
        self.start_time = time.time()
        
    def add_request(self, client_ip):
        with self.lock:
            current_time = time.time()
            self.requests.append((current_time, client_ip))
            self.request_count += 1
            
            # Keep only requests from last 60 seconds
            while self.requests and current_time - self.requests[0][0] > 60:
                self.requests.popleft()
    
    def get_stats(self):
        with self.lock:
            current_time = time.time()
            recent_requests = len(self.requests)
            uptime = current_time - self.start_time
            avg_rps = self.request_count / uptime if uptime > 0 else 0
            
            # Count requests by IP in last 60 seconds
            ip_counts = defaultdict(int)
            for _, ip in self.requests:
                ip_counts[ip] += 1
            
            return {
                'total_requests': self.request_count,
                'recent_requests_60s': recent_requests,
                'uptime_seconds': uptime,
                'average_rps': avg_rps,
                'current_rps': recent_requests / min(60, uptime) if uptime > 0 else 0,
                'top_ips': dict(sorted(ip_counts.items(), key=lambda x: x[1], reverse=True)[:5])
            }

class TestServerHandler(BaseHTTPRequestHandler):
    tracker = RequestTracker()
    
    def do_GET(self):
        client_ip = self.client_address[0]
        self.tracker.add_request(client_ip)
        
        # Log request
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {client_ip} -> {self.path}")
        
        if self.path == '/':
            self.send_home_page()
        elif self.path == '/stats':
            self.send_stats()
        elif self.path == '/health':
            self.send_health()
        else:
            self.send_404()
    
    def send_home_page(self):
        stats = self.tracker.get_stats()
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>DDoS Test Server</title>
            <meta http-equiv="refresh" content="2">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }}
                .container {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .stats {{ background: #e8f4f8; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                .metric {{ margin: 5px 0; }}
                .high-load {{ color: #d63031; font-weight: bold; }}
                .normal-load {{ color: #00b894; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎯 DDoS Test Server</h1>
                <p>This server is running for educational DDoS testing purposes.</p>
                
                <div class="warning">
                    <strong>⚠️ Educational Use Only:</strong> This server is designed for learning about DDoS attacks and server load testing.
                </div>
                
                <div class="stats">
                    <h3>📊 Server Statistics</h3>
                    <div class="metric">Total Requests: <strong>{stats['total_requests']}</strong></div>
                    <div class="metric">Requests (Last 60s): <strong class="{'high-load' if stats['recent_requests_60s'] > 100 else 'normal-load'}">{stats['recent_requests_60s']}</strong></div>
                    <div class="metric">Current RPS: <strong class="{'high-load' if stats['current_rps'] > 10 else 'normal-load'}">{stats['current_rps']:.1f}</strong></div>
                    <div class="metric">Average RPS: <strong>{stats['average_rps']:.1f}</strong></div>
                    <div class="metric">Uptime: <strong>{stats['uptime_seconds']:.1f}s</strong></div>
                </div>
                
                <div class="stats">
                    <h3>🌐 Top Client IPs (Last 60s)</h3>
                    {''.join([f'<div class="metric">{ip}: <strong>{count}</strong> requests</div>' for ip, count in stats['top_ips'].items()])}
                </div>
                
                <p><strong>Server Status:</strong> 
                    <span class="{'high-load' if stats['current_rps'] > 20 else 'normal-load'}">
                        {'🔥 Under Heavy Load!' if stats['current_rps'] > 20 else '✅ Normal Operation'}
                    </span>
                </p>
                
                <p><small>Auto-refreshes every 2 seconds | <a href="/stats">JSON Stats</a> | <a href="/health">Health Check</a></small></p>
            </div>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def send_stats(self):
        stats = self.tracker.get_stats()
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(stats, indent=2).encode())
    
    def send_health(self):
        stats = self.tracker.get_stats()
        status = "healthy" if stats['current_rps'] < 50 else "overloaded"
        
        health_data = {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "current_rps": stats['current_rps'],
            "recent_requests": stats['recent_requests_60s']
        }
        
        self.send_response(200 if status == "healthy" else 503)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(health_data, indent=2).encode())
    
    def send_404(self):
        self.send_response(404)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<h1>404 - Page Not Found</h1>')
    
    def log_message(self, format, *args):
        # Suppress default logging to avoid spam
        pass

def run_server(port=8080):
    server_address = ('', port)
    httpd = HTTPServer(server_address, TestServerHandler)
    
    print("=" * 60)
    print("🚀 DDoS Test Server Starting")
    print("=" * 60)
    print(f"Server running on: http://localhost:{port}")
    print(f"Stats endpoint: http://localhost:{port}/stats")
    print(f"Health endpoint: http://localhost:{port}/health")
    print("=" * 60)
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        httpd.server_close()

if __name__ == '__main__':
    import sys
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8080
    run_server(port)
